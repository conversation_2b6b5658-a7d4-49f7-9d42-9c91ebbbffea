{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run",
            "type": "debugpy",
            "request": "launch",
            "program": "/fs-computility/llmeval/zhudongsheng/program/tau-bench/run.py",
            "python": "/fs-computility/llmeval/zhudongsheng/miniconda3/envs/tau_bench/bin/python",
            "args": [
                "--agent-strategy", "tool-calling",
                "--env", "retail",
                "--model", "gemini-2.5-pro-preview-05-06",
                "--model-provider", "openai",
                "--base-url", "https://generativelanguage.googleapis.com/v1beta/openai/",
                "--temperature", "0.6", 
                "--top-p", "0.95", 
                "--user-model", "gpt-4o",
                "--user-model-provider","openai",
                "--user-strategy", "llm",
                "--max-concurrency", "1",
            ],
            "console": "integratedTerminal",
            "env": {
                // // "CUDA_VISIBLE_DEVICES": "0,1",
                "OPENAI_API_KEY": "AIzaSyDMti4Dz9BhC2rBbS0BAWpSNyu0bR-Yxeo", 
                "DEPLOY_BASE_URL": "http://172.30.52.140:23333/v1",
                "DEPLOY_API_KEY": "YOUR_API_KEY",
                "OPENAI_PROXY_URL": "http://closeai-proxy.pjlab.org.cn:23128", 
                // "HTTP_PROXY": "http://closeai-proxy.pjlab.org.cn:23128", 
                // "https_proxy": "http://closeai-proxy.pjlab.org.cn:23128",
                // "http_proxy": "http://closeai-proxy.pjlab.org.cn:23128",
            }
        }
    ]
}