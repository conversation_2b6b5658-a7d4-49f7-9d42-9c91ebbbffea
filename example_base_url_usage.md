# Base URL Parameter Usage Examples

The `--base-url` parameter has been added to allow you to control the OpenAI client's base URL. This is useful for:

1. Using custom OpenAI-compatible API endpoints
2. Using local model servers (like vLLM, Ollama, etc.)
3. Using proxy servers or custom deployments

## Usage Examples

### 1. Using a custom deployment endpoint

```bash
python run.py \
    --agent-strategy tool-calling \
    --env retail \
    --model gpt-4.1-nano-2025-04-14 \
    --model-provider openai \
    --base-url http://*************:23333/v1 \
    --temperature 0.6 \
    --user-model gpt-4o \
    --user-model-provider openai \
    --max-concurrency 10
```

### 2. Using a local vLLM server

```bash
python run.py \
    --agent-strategy tool-calling \
    --env retail \
    --model meta-llama/Llama-2-7b-chat-hf \
    --model-provider openai \
    --base-url http://localhost:8000/v1 \
    --temperature 0.6 \
    --user-model gpt-4o \
    --user-model-provider openai \
    --max-concurrency 10
```

### 3. Using default OpenAI API (no base-url needed)

```bash
python run.py \
    --agent-strategy tool-calling \
    --env retail \
    --model gpt-4o \
    --model-provider openai \
    --temperature 0.6 \
    --user-model gpt-4o \
    --user-model-provider openai \
    --max-concurrency 10
```

## How it works

- When `--base-url` is provided, it overrides the default OpenAI API endpoint
- When `--base-url` is not provided, the system uses the default OpenAI API endpoint (`https://api.openai.com/v1`)
- The parameter works with all agent strategies: `tool-calling`, `react`, `act`, and `few-shot`
- The parameter only affects the agent model, not the user model (user model always uses standard providers)

## Environment Variables vs Command Line

You can still use environment variables for deployed models:

```bash
export DEPLOY_API_KEY=your_api_key
export DEPLOY_BASE_URL=http://your-endpoint/v1

python run.py \
    --agent-strategy tool-calling \
    --env retail \
    --model your-model \
    --model-provider deployed \
    --user-model gpt-4o \
    --user-model-provider openai \
    --max-concurrency 10
```

But now you can also use the command line parameter for more flexibility:

```bash
python run.py \
    --agent-strategy tool-calling \
    --env retail \
    --model your-model \
    --model-provider openai \
    --base-url http://your-endpoint/v1 \
    --user-model gpt-4o \
    --user-model-provider openai \
    --max-concurrency 10
```
