# Copyright Sierra

import json
import httpx
from litellm import completion
from typing import List, Optional, Dict, Any
import os
import openai
import time
import random
from functools import wraps

from tau_bench.agents.base import Agent
from tau_bench.envs.base import Env
from tau_bench.types import SolveResult, Action, RESPOND_ACTION_NAME
from tau_bench.utils.llm_params import build_completion_params, build_openai_api_params


def retry_on_api_error(max_retries: int = 3, base_delay: float = 1.0):
    """Decorator to retry API calls with exponential backoff."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    # Check if it's a retryable error
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in [
                        'rate limit', 'timeout', 'connection', 'server error',
                        'internal error', 'service unavailable', 'too many requests',
                        'temporary', 'retry'
                    ]):
                        if attempt < max_retries:
                            # Exponential backoff with jitter
                            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                            print(f"API call failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                            print(f"Retrying in {delay:.2f} seconds...")
                            time.sleep(delay)
                            continue
                    # If not retryable or max retries reached, raise the exception
                    raise e
            # This should never be reached, but just in case
            raise last_exception
        return wrapper
    return decorator


class ToolCallingAgent(Agent):
    def __init__(
        self,
        tools_info: List[Dict[str, Any]],
        wiki: str,
        model: str,
        provider: str,
        temperature: float = 0.0,
        top_p: float | None = None,
        top_k: int | None = None,
        base_url: str | None = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        self.tools_info = tools_info
        self.wiki = wiki
        self.model = model
        self.provider = provider
        self.temperature = temperature
        self.top_p = top_p
        self.top_k = top_k
        self.base_url = base_url
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def solve(
        self, env: Env, task_index: Optional[int] = None, max_num_steps: int = 30
    ) -> SolveResult:
        total_cost = 0.0
        env_reset_res = env.reset(task_index=task_index)
        obs = env_reset_res.observation
        info = env_reset_res.info.model_dump()
        reward = 0.0
        messages: List[Dict[str, Any]] = [
            {"role": "system", "content": self.wiki},
            {"role": "user", "content": obs},
        ]
        for _ in range(max_num_steps):
            if self.provider != "deployed":
                if self.provider == "openai":
                    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
                    proxy = os.getenv("OPENAI_PROXY_URL")

                    # Use provided base_url or default to OpenAI API
                    client_base_url = self.base_url if self.base_url is not None else "https://api.openai.com/v1"

                    # Only pass http_client if proxy is configured
                    if proxy:
                        client = openai.Client(
                            base_url=client_base_url,
                            api_key=OPENAI_API_KEY,
                            http_client=httpx.Client(proxy=proxy))
                    else:
                        client = openai.Client(
                            base_url=client_base_url,
                            api_key=OPENAI_API_KEY)
                    # Build API call parameters using helper function
                    api_params = build_openai_api_params(
                        model=self.model,
                        messages=messages,
                        temperature=self.temperature,
                        top_p=self.top_p,
                        top_k=self.top_k,
                        tools=self.tools_info,
                    )

                    # Apply retry logic to API call
                    @retry_on_api_error(max_retries=self.max_retries, base_delay=self.retry_delay)
                    def make_api_call():
                        return client.chat.completions.create(**api_params)

                    res = make_api_call()
                    # 手动补充 _hidden_params 字段
                    if not hasattr(res, "_hidden_params"):
                        res._hidden_params = {}
                    # 兼容对象类型的 usage
                    total_tokens = getattr(res, "usage", None)
                    if total_tokens is not None:
                        total_tokens = getattr(res.usage, "total_tokens", 0)
                    else:
                        total_tokens = 0
                    res._hidden_params["response_cost"] = total_tokens * 0.0
                else:
                    # Build completion parameters using helper function
                    completion_params = build_completion_params(
                        model=self.model,
                        provider=self.provider,
                        messages=messages,
                        temperature=self.temperature,
                        top_p=self.top_p,
                        top_k=self.top_k,
                        tools=self.tools_info,
                    )

                    # Apply retry logic to API call
                    @retry_on_api_error(max_retries=self.max_retries, base_delay=self.retry_delay)
                    def make_completion_call():
                        return completion(**completion_params)

                    res = make_completion_call()
            else:
                deploy_api_key = os.getenv("DEPLOY_API_KEY", "")
                deploy_base_url = os.getenv("DEPLOY_BASE_URL", "")
                client = openai.Client(
                    base_url=deploy_base_url,
                    api_key=deploy_api_key
                )
                # Build API call parameters using helper function
                api_params = build_openai_api_params(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    top_p=self.top_p,
                    top_k=self.top_k,
                    tools=self.tools_info,
                )

                # Apply retry logic to API call
                @retry_on_api_error(max_retries=self.max_retries, base_delay=self.retry_delay)
                def make_deployed_api_call():
                    return client.chat.completions.create(**api_params)

                res = make_deployed_api_call()
                # 手动补充 _hidden_params 字段
                if not hasattr(res, "_hidden_params"):
                    res._hidden_params = {}
                # 兼容对象类型的 usage
                total_tokens = getattr(res, "usage", None)
                if total_tokens is not None:
                    total_tokens = getattr(res.usage, "total_tokens", 0)
                else:
                    total_tokens = 0
                res._hidden_params["response_cost"] = total_tokens * 0.0

            next_message = res.choices[0].message.model_dump()
            total_cost += res._hidden_params["response_cost"]
            action = message_to_action(next_message)
            env_response = env.step(action)
            reward = env_response.reward
            info = {**info, **env_response.info.model_dump()}
            if action.name != RESPOND_ACTION_NAME:
                next_message["tool_calls"] = next_message["tool_calls"][:1]
                messages.extend(
                    [
                        next_message,
                        {
                            "role": "tool",
                            "tool_call_id": next_message["tool_calls"][0]["id"],
                            "name": next_message["tool_calls"][0]["function"]["name"],
                            "content": env_response.observation,
                        },
                    ]
                )
            else:
                messages.extend(
                    [
                        next_message,
                        {"role": "user", "content": env_response.observation},
                    ]
                )
            if env_response.done:
                break
        return SolveResult(
            reward=reward,
            info=info,
            messages=messages,
            total_cost=total_cost,
        )


def message_to_action(
    message: Dict[str, Any],
) -> Action:
    if "tool_calls" in message and message["tool_calls"] is not None and len(message["tool_calls"]) > 0 and message["tool_calls"][0]["function"] is not None:
        tool_call = message["tool_calls"][0]
        return Action(
            name=tool_call["function"]["name"],
            kwargs=json.loads(tool_call["function"]["arguments"]),
        )
    else:
        return Action(name=RESPOND_ACTION_NAME, kwargs={"content": message["content"]})
