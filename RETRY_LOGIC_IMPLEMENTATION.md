# Model Retry Logic Implementation

## Overview

This implementation adds comprehensive retry logic to all model API calls in the tau-bench project. The retry mechanism uses exponential backoff with jitter to handle temporary API failures gracefully.

## Files Modified

### 1. Agent Files
- **tau_bench/agents/tool_calling_agent.py**
- **tau_bench/agents/chat_react_agent.py** 
- **tau_bench/agents/few_shot_agent.py**

### 2. Model Files
- **tau_bench/model_utils/model/openai.py**
- **tau_bench/model_utils/model/mistral.py**

## Features Added

### 1. Retry Decorator
A configurable retry decorator `retry_on_api_error()` that:
- Implements exponential backoff with jitter
- Identifies retryable vs non-retryable errors
- Provides detailed logging of retry attempts
- Supports configurable max retries and base delay

### 2. Retryable Error Detection
The system automatically retries on errors containing these keywords:
- `rate limit`
- `timeout`
- `connection`
- `server error`
- `internal error`
- `service unavailable`
- `too many requests`
- `temporary`
- `retry`

### 3. Exponential Backoff
- Base delay starts at configurable value (default: 1.0 seconds)
- Each retry doubles the delay: delay = base_delay * (2 ^ attempt)
- Random jitter (0-1 seconds) added to prevent thundering herd
- Example delays: 1s, 2s, 4s, 8s...

### 4. Configuration Parameters
All agents and models now accept:
- `max_retries`: Maximum number of retry attempts (default: 3)
- `retry_delay`: Base delay in seconds (default: 1.0)

## Usage Examples

### Agent Usage
```python
# Tool calling agent with custom retry settings
agent = ToolCallingAgent(
    tools_info=tools,
    wiki=wiki,
    model="gpt-4o",
    provider="openai",
    max_retries=5,      # Retry up to 5 times
    retry_delay=2.0     # Start with 2 second delay
)

# React agent with default retry settings
agent = ChatReActAgent(
    tools_info=tools,
    wiki=wiki,
    model="claude-3-sonnet",
    provider="anthropic"
    # Uses default: max_retries=3, retry_delay=1.0
)
```

### Model Usage
```python
# OpenAI model with custom retry settings
model = OpenAIModel(
    model="gpt-4o",
    max_retries=4,
    retry_delay=1.5
)

# Mistral model with default retry settings
model = MistralModel(
    model="mistral-large-latest"
    # Uses default: max_retries=3, retry_delay=1.0
)
```

## Implementation Details

### 1. Agent-Level Retry
Each agent applies retry logic to:
- OpenAI API calls (when provider="openai")
- LiteLLM completion calls (for other providers)
- Deployed API calls (when provider="deployed")

### 2. Model-Level Retry
Model classes apply retry logic to:
- Direct API client calls
- Both sync and async operations (where applicable)

### 3. Error Handling
- Non-retryable errors (e.g., invalid API key) fail immediately
- Retryable errors trigger exponential backoff
- After max retries, the original exception is raised
- Detailed logging shows each retry attempt and delay

## Benefits

1. **Improved Reliability**: Handles temporary API failures automatically
2. **Rate Limit Handling**: Respects API rate limits with backoff
3. **Network Resilience**: Recovers from temporary network issues
4. **Configurable**: Allows tuning retry behavior per use case
5. **Backward Compatible**: Default parameters maintain existing behavior

## Testing

A test script `test_retry_logic.py` demonstrates:
- Rate limit error recovery
- Timeout error recovery  
- Non-retryable error immediate failure
- Retry exhaustion scenarios

Run the test with:
```bash
python test_retry_logic.py
```

## Integration with Existing Code

The retry logic is seamlessly integrated:
- Existing code continues to work without changes
- Optional parameters allow customization when needed
- Error messages include retry information for debugging
- No performance impact when APIs work normally

## Future Enhancements

Potential improvements:
1. Configurable retry error patterns
2. Per-error-type retry strategies
3. Circuit breaker pattern for persistent failures
4. Metrics collection for retry statistics
5. Async retry support for async model operations
