#!/usr/bin/env python3
"""
Test script to demonstrate the retry logic functionality.
This script shows how the retry mechanism works with different error scenarios.
"""

import time
import random
from functools import wraps


def retry_on_api_error(max_retries: int = 3, base_delay: float = 1.0):
    """Decorator to retry API calls with exponential backoff."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    # Check if it's a retryable error
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in [
                        'rate limit', 'timeout', 'connection', 'server error', 
                        'internal error', 'service unavailable', 'too many requests',
                        'temporary', 'retry'
                    ]):
                        if attempt < max_retries:
                            # Exponential backoff with jitter
                            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                            print(f"API call failed (attempt {attempt + 1}/{max_retries + 1}): {e}")
                            print(f"Retrying in {delay:.2f} seconds...")
                            time.sleep(delay)
                            continue
                    # If not retryable or max retries reached, raise the exception
                    raise e
            # This should never be reached, but just in case
            raise last_exception
        return wrapper
    return decorator


class MockAPIClient:
    """Mock API client to simulate different error scenarios."""
    
    def __init__(self, fail_count: int = 2, error_type: str = "rate_limit"):
        self.fail_count = fail_count
        self.call_count = 0
        self.error_type = error_type
    
    @retry_on_api_error(max_retries=3, base_delay=0.5)
    def make_api_call(self, data: str):
        """Simulate an API call that may fail."""
        self.call_count += 1
        print(f"Making API call #{self.call_count} with data: {data}")
        
        if self.call_count <= self.fail_count:
            if self.error_type == "rate_limit":
                raise Exception("Rate limit exceeded. Please retry after some time.")
            elif self.error_type == "timeout":
                raise Exception("Connection timeout occurred")
            elif self.error_type == "server_error":
                raise Exception("Internal server error - temporary issue")
            elif self.error_type == "non_retryable":
                raise Exception("Invalid API key")
        
        return f"Success! Processed: {data}"


def test_retry_scenarios():
    """Test different retry scenarios."""
    
    print("=" * 60)
    print("Testing Retry Logic for Model API Calls")
    print("=" * 60)
    
    # Test 1: Rate limit error that succeeds after retries
    print("\n1. Testing rate limit error (should succeed after 2 retries):")
    client1 = MockAPIClient(fail_count=2, error_type="rate_limit")
    try:
        result = client1.make_api_call("test data 1")
        print(f"Final result: {result}")
    except Exception as e:
        print(f"Failed after all retries: {e}")
    
    # Test 2: Timeout error that succeeds after 1 retry
    print("\n2. Testing timeout error (should succeed after 1 retry):")
    client2 = MockAPIClient(fail_count=1, error_type="timeout")
    try:
        result = client2.make_api_call("test data 2")
        print(f"Final result: {result}")
    except Exception as e:
        print(f"Failed after all retries: {e}")
    
    # Test 3: Non-retryable error (should fail immediately)
    print("\n3. Testing non-retryable error (should fail immediately):")
    client3 = MockAPIClient(fail_count=1, error_type="non_retryable")
    try:
        result = client3.make_api_call("test data 3")
        print(f"Final result: {result}")
    except Exception as e:
        print(f"Failed immediately (non-retryable): {e}")
    
    # Test 4: Too many failures (should exhaust retries)
    print("\n4. Testing too many failures (should exhaust retries):")
    client4 = MockAPIClient(fail_count=5, error_type="server_error")
    try:
        result = client4.make_api_call("test data 4")
        print(f"Final result: {result}")
    except Exception as e:
        print(f"Failed after exhausting retries: {e}")
    
    print("\n" + "=" * 60)
    print("Retry logic testing completed!")
    print("=" * 60)


if __name__ == "__main__":
    test_retry_scenarios()
